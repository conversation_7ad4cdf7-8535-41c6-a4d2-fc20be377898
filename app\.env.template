# Environment Configuration Template for CCALC App
# Copy this to .env and update the values for your environment

# Backend URL - Change this to your ngrok URL when building
EXPO_PUBLIC_BACKEND_URL=http://*************:3000

# Frontend URL - Change this to your ngrok frontend URL when building  
EXPO_PUBLIC_FRONTEND_URL=http://localhost:3001

# Build Environment (development, preview, production)
EXPO_PUBLIC_BUILD_ENV=development

# App Version
EXPO_PUBLIC_APP_VERSION=1.0.0

# Debug flags
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_NETWORK=true

# For GitHub Actions builds, these will be set via secrets:
# EXPO_PUBLIC_BACKEND_URL=https://your-ngrok-url.ngrok.io
# EXPO_PUBLIC_FRONTEND_URL=https://your-frontend-url.ngrok.io
