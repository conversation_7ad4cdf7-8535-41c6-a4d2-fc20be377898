export default {
  expo: {
    name: "<PERSON><PERSON><PERSON>",
    slug: "ccalc-app",
    version: process.env.EXPO_PUBLIC_APP_VERSION || "1.0.0",
    sdkVersion: "53.0.0",
    scheme: "ccalc",
    orientation: "portrait",
    userInterfaceStyle: "light",
    platforms: ["ios", "android"],
    assetBundlePatterns: [
      "**/*"
    ],
    plugins: [
      "expo-secure-store",
      "expo-av",
      [
        "expo-image-picker",
        {
          "photosPermission": "The app accesses your photos to allow secure media sharing.",
          "cameraPermission": "The app accesses your camera to allow secure photo capture.",
          "microphonePermission": "The app accesses your microphone for secure voice calls."
        }
      ]
    ],
    ios: {
      supportsTablet: false,
      bundleIdentifier: "com.ccalc.app",
      buildNumber: process.env.EXPO_PUBLIC_APP_VERSION || "1.0.0",
      deploymentTarget: "13.4",
      scheme: "ccalc",
      infoPlist: {
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true,
          NSAllowsLocalNetworking: true,
          NSExceptionDomains: {
            // Dynamic ngrok domain support
            ...(process.env.EXPO_PUBLIC_BACKEND_URL && process.env.EXPO_PUBLIC_BACKEND_URL.includes('ngrok.io') ? {
              [new URL(process.env.EXPO_PUBLIC_BACKEND_URL).hostname]: {
                NSExceptionAllowsInsecureHTTPLoads: true,
                NSExceptionMinimumTLSVersion: "1.0",
                NSExceptionRequiresForwardSecrecy: false
              }
            } : {}),
            "*************": {
              NSExceptionAllowsInsecureHTTPLoads: true,
              NSExceptionMinimumTLSVersion: "1.0",
              NSExceptionRequiresForwardSecrecy: false
            },
            "localhost": {
              NSExceptionAllowsInsecureHTTPLoads: true,
              NSExceptionMinimumTLSVersion: "1.0",
              NSExceptionRequiresForwardSecrecy: false
            }
          }
        }
      }
    },
    android: {
      package: "com.ccalc.app",
      versionCode: 1,
      compileSdkVersion: 34,
      targetSdkVersion: 34,
      permissions: [
        "android.permission.RECORD_AUDIO",
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE"
      ]
    },
    extra: {
      backendUrl: process.env.EXPO_PUBLIC_BACKEND_URL || "http://localhost:3000",
      frontendUrl: process.env.EXPO_PUBLIC_FRONTEND_URL || "http://localhost:3001",
      buildEnv: process.env.EXPO_PUBLIC_BUILD_ENV || "development",
      debugMode: process.env.EXPO_PUBLIC_DEBUG_MODE === "true",
      logNetwork: process.env.EXPO_PUBLIC_LOG_NETWORK === "true",
      eas: {
        projectId: "d937e08b-eada-4b2b-8916-c9ff678d8347"
      }
    },
    jsEngine: "hermes"
  }
};
