import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { withAdminAuth } from '../../contexts/AdminAuthContext';
import apiClient from '../../utils/apiClient';
import VoiceNeutralization from '../VoiceNeutralization';
import WorldVoiceModulationPanel from './WorldVoiceModulationPanel';
import EnhancedVoiceModulationPanel from './EnhancedVoiceModulationPanel';
import VoiceRecordingsManagement from './VoiceRecordingsManagement';
import {
  Add as PlusIcon,
  Edit as PencilIcon,
  Delete as TrashIcon,
  Visibility as EyeIcon,
  Check as CheckIcon,
  Close as XMarkIcon,
  Warning as ExclamationTriangleIcon,
  PersonAdd as UserPlusIcon,
  People as UsersIcon,
  LockOpen as LockOpenIcon,
  Refresh as ArrowPathIcon
} from '@mui/icons-material';

interface UserDetails {
  _id: string;
  username: string;
  email: string;
  isActive: boolean;
  status: string;
  createdAt: string;
  lastLogin: string;
  devices: Device[];
  loginHistory: LoginHistory[];
  chatHistory: ChatSession[];
  voiceRecordings: VoiceRecording[];
  securityEvents: SecurityEvent[];
  mathExpression: {
    expression: string;
    type: string;
    updatedAt: string;
  };
  profile: {
    displayName: string;
  };
  isSuperuser: boolean;
}

interface Device {
  deviceId: string;
  fingerprint: string;
  deviceType: string;
  deviceModel?: string;
  os?: string;
  browser?: string;
  lastUsed: string;
  isActive: boolean;
  bleDevices: BLEDevice[];
}

interface BLEDevice {
  deviceId: string;
  deviceName: string;
  pairedAt: string;
  lastConnected: string;
  isVerified: boolean;
}

interface LoginHistory {
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  deviceFingerprint: string;
  location?: {
    country: string;
    city: string;
  };
  success: boolean;
  failureReason?: string;
}

interface ChatSession {
  sessionId: string;
  startedAt: string;
  endedAt?: string;
  messageCount: number;
  voiceCallDuration: number;
  encryptionUsed: boolean;
}

interface ChatMessage {
  _id: string;
  senderId: {
    _id: string;
    username: string;
    profile?: { displayName: string };
    isSuperuser: boolean;
  };
  recipientId: {
    _id: string;
    username: string;
    profile?: { displayName: string };
    isSuperuser: boolean;
  };
  content: {
    text: string;
    encrypted?: string;
    iv?: string;
    salt?: string;
  };
  messageType: 'text' | 'media' | 'system' | 'ephemeral';
  mediaAttachment?: {
    filename: string;
    mimeType: string;
    size: number;
    thumbnailPath?: string;
  };
  isEncrypted: boolean;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  createdAt: string;
  updatedAt: string;
}

interface VoiceRecording {
  recordingId: string;
  sessionId: string;
  timestamp: string;
  duration: number;
  fileSize: number;
  voiceProfile: string;
  isProcessed: boolean;
}

interface SecurityEvent {
  eventType: string;
  timestamp: string;
  details: any;
  severity: string;
  resolved: boolean;
}

interface CreateUserForm {
  username: string;
  email: string;
  expression: string;
  displayName: string;
  isSuperuser: boolean;
}

interface EditUserForm {
  displayName: string;
  expression: string;
  isSuperuser: boolean;
  status: string;
}

interface PasswordConfirmation {
  password: string;
}

const UsersManagement: React.FC = () => {
  const [users, setUsers] = useState<UserDetails[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'devices' | 'chat' | 'voice' | 'modulation' | 'recordings' | 'security' | 'logs'>('overview');
  const [worldVoiceModulationOpen, setWorldVoiceModulationOpen] = useState(false);
  const [enhancedVoiceModulationOpen, setEnhancedVoiceModulationOpen] = useState(false);
  const [currentVoiceProfile, setCurrentVoiceProfile] = useState<string>('SECURE_DEEP_MALE');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Chat-related state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatLoading, setChatLoading] = useState(false);
  const [chatError, setChatError] = useState<string | null>(null);

  // Voice recordings state
  const [voiceRecordings, setVoiceRecordings] = useState<VoiceRecording[]>([]);
  const [voiceLoading, setVoiceLoading] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [playingRecording, setPlayingRecording] = useState<string | null>(null);

  // Device details state
  const [deviceDetails, setDeviceDetails] = useState<any[]>([]);
  const [deviceLoading, setDeviceLoading] = useState(false);
  const [deviceError, setDeviceError] = useState<string | null>(null);
  const [expandedDevice, setExpandedDevice] = useState<string | null>(null);

  // Voice settings state
  const [voiceSettings, setVoiceSettings] = useState<any>(null);
  const [voiceProfiles, setVoiceProfiles] = useState<string[]>([]);
  const [enhancedProfiles, setEnhancedProfiles] = useState<any>(null);
  const [voiceSettingsLoading, setVoiceSettingsLoading] = useState(false);
  const [voiceSettingsError, setVoiceSettingsError] = useState<string | null>(null);

  // Voice testing state
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // CRUD Modal States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingUser, setEditingUser] = useState<UserDetails | null>(null);

  // Form States
  const [createForm, setCreateForm] = useState<CreateUserForm>({
    username: '',
    email: '',
    expression: '',
    displayName: '',
    isSuperuser: false
  });
  const [editForm, setEditForm] = useState<EditUserForm>({
    displayName: '',
    expression: '',
    isSuperuser: false,
    status: 'pending_device_registration'
  });

  // Selection and Bulk Operations
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Password Confirmation
  const [passwordConfirm, setPasswordConfirm] = useState<PasswordConfirmation>({
    password: ''
  });

  // Loading States
  const [submitting, setSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [actionLoading, setActionLoading] = useState<Record<string, boolean>>({});
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [showActionConfirm, setShowActionConfirm] = useState(false);
  const [pendingAction, setPendingAction] = useState<{userId: string, action: string, actionLabel: string} | null>(null);

  // Admin Actions
  const handleAdminActionConfirm = (userId: string, action: string) => {
    const actionLabels: Record<string, string> = {
      'reset-devices': 'Reset all devices',
      'reset-ble': 'Reset BLE devices',
      'reset-sessions': 'Reset chat sessions',
      'unlock': 'Unlock user',
      'reset-expression': 'Reset math expression'
    };
    
    setPendingAction({
      userId,
      action,
      actionLabel: actionLabels[action] || action
    });
    setShowActionConfirm(true);
  };

  const executeAdminAction = async () => {
    if (!pendingAction) return;
    
    try {
      setActionLoading(prev => ({ ...prev, [pendingAction.userId]: true }));
      setFormErrors({}); // Clear any previous errors
      setSuccessMessage(''); // Clear any previous success messages
      
      const endpoint = `/api/admin/users/${pendingAction.userId}/${pendingAction.action}`;
      await apiClient.post(endpoint);
      
      // Refresh the users list to show updated data
      await fetchUsers();
      
      // Show success message
      setSuccessMessage(`${pendingAction.actionLabel} completed successfully`);
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
      
    } catch (error: any) {
      console.error(`Error performing ${pendingAction.action}:`, error);
      setFormErrors({
        submit: error.response?.data?.error || error.message || `Failed to ${pendingAction.action.replace('-', ' ')}`
      });
    } finally {
      setActionLoading(prev => ({ ...prev, [pendingAction.userId]: false }));
      setShowActionConfirm(false);
      setPendingAction(null);
    }
  };

  const handleAdminAction = async (userId: string, action: string) => {
    // Special handling for reset-expression action
    if (action === 'reset-expression') {
      const user = users.find(u => u._id === userId);
      if (!user) return;
      
      const newExpression = prompt(
        `Enter new mathematical expression for user "${user.username}":\n\n` +
        `Examples: 12+5*7-3*2+8/4, (15+3)*2-8/4, 25*3-10+6/2\n\n` +
        `Current expression: ${user.mathExpression?.expression || 'Not set'}`
      );
      
      if (newExpression === null) return; // User cancelled
      
      if (!newExpression.trim()) {
        alert('Expression cannot be empty');
        return;
      }
      
      // Basic validation for mathematical expression
      if (!/[\+\-\*\/\(\)\d]/.test(newExpression.trim())) {
        alert('Expression must contain valid mathematical operations (+, -, *, /, parentheses, numbers)');
        return;
      }
      
      await handleResetUserExpression(userId, user.username, newExpression);
      return;
    }
    
    // For non-destructive actions, execute immediately
    if (action === 'unlock') {
      try {
        setActionLoading(prev => ({ ...prev, [userId]: true }));
        setFormErrors({});
        setSuccessMessage('');
        
        const endpoint = `/api/admin/users/${userId}/${action}`;
        await apiClient.post(endpoint);
        
        await fetchUsers();
        setSuccessMessage('User unlocked successfully');
        setTimeout(() => setSuccessMessage(''), 3000);
        
      } catch (error: any) {
        console.error(`Error performing ${action}:`, error);
        setFormErrors({
          submit: error.response?.data?.error || error.message || `Failed to unlock user`
        });
      } finally {
        setActionLoading(prev => ({ ...prev, [userId]: false }));
      }
    } else {
      // For destructive actions, show confirmation
      handleAdminActionConfirm(userId, action);
    }
  };

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/admin/users/detailed');
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Fetch chat messages for selected user
  const fetchChatMessages = async (userId: string) => {
    try {
      setChatLoading(true);
      setChatError(null);

      console.log('Fetching chat messages for user:', userId);

      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/users/${userId}/chat-messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Chat messages response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Chat messages error response:', errorText);
        throw new Error(`Failed to fetch chat messages: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Chat messages data:', data);
      setChatMessages(data.messages || []);
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      setChatError(error instanceof Error ? error.message : 'Failed to load chat messages');
      setChatMessages([]);
    } finally {
      setChatLoading(false);
    }
  };

  // Fetch chat messages when user is selected and chat tab is active
  useEffect(() => {
    if (selectedUser && activeTab === 'chat') {
      fetchChatMessages(selectedUser._id);
    }
  }, [selectedUser, activeTab]);

  // Fetch voice recordings for selected user
  const fetchVoiceRecordings = async (userId: string) => {
    try {
      setVoiceLoading(true);
      setVoiceError(null);

      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/users/${userId}/voice-recordings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch voice recordings');
      }

      const data = await response.json();
      setVoiceRecordings(data.recordings || []);
    } catch (error) {
      console.error('Error fetching voice recordings:', error);
      setVoiceError(error instanceof Error ? error.message : 'Failed to load voice recordings');
      setVoiceRecordings([]);
    } finally {
      setVoiceLoading(false);
    }
  };

  // Fetch voice recordings when user is selected and voice tab is active
  useEffect(() => {
    if (selectedUser && activeTab === 'voice') {
      fetchVoiceRecordings(selectedUser._id);
    }
  }, [selectedUser, activeTab]);

  // Fetch device details for selected user
  const fetchDeviceDetails = async (userId: string) => {
    try {
      setDeviceLoading(true);
      setDeviceError(null);

      console.log('Fetching device details for user:', userId);

      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/users/${userId}/device-details`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Device details response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Device details error response:', errorText);
        throw new Error(`Failed to fetch device details: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Device details data:', data);
      setDeviceDetails(data.devices || []);
    } catch (error) {
      console.error('Error fetching device details:', error);
      setDeviceError(error instanceof Error ? error.message : 'Failed to load device details');
      setDeviceDetails([]);
    } finally {
      setDeviceLoading(false);
    }
  };

  // Fetch device details when user is selected and devices tab is active
  useEffect(() => {
    if (selectedUser && activeTab === 'devices') {
      fetchDeviceDetails(selectedUser._id);
    }
  }, [selectedUser, activeTab]);

  // Fetch voice settings for selected user
  const fetchVoiceSettings = async (userId: string) => {
    try {
      setVoiceSettingsLoading(true);
      setVoiceSettingsError(null);

      console.log('Fetching voice settings for user:', userId);

      // Fetch user voice settings
      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const userResponse = await fetch(`/api/admin/users/${userId}/voice-settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Voice settings response status:', userResponse.status);

      if (!userResponse.ok) {
        const errorText = await userResponse.text();
        console.error('Voice settings error response:', errorText);
        throw new Error(`Failed to fetch voice settings: ${userResponse.status} ${errorText}`);
      }

      const userData = await userResponse.json();
      console.log('Voice settings data:', userData);
      setVoiceSettings(userData.settings);

      // Fetch enhanced voice profiles
      try {
        const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
        const profilesResponse = await fetch('/api/voice/profiles-enhanced', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (profilesResponse.ok) {
          const profilesData = await profilesResponse.json();
          setEnhancedProfiles(profilesData.data);

          // Filter profiles based on user type
          const user = users.find(u => u._id === userId);
          const userRole = user?.isSuperuser ? 'superuser' : 'user';

          const availableProfiles = profilesData.data.standardProfiles
            .filter((profile: any) => {
              // If profile is users_only and user is superuser, exclude it
              if (profile.userType === "users_only" && userRole === "superuser") {
                return false;
              }
              return true;
            })
            .map((profile: any) => profile.name);

          setVoiceProfiles(availableProfiles);
        } else {
          // Fallback to default profiles if enhanced profiles fail
          const fallbackProfiles = ['SECURE_MALE', 'SECURE_FEMALE', 'ROBOTIC', 'DEEP_SECURE', 'ANONYMOUS'];
          setVoiceProfiles(fallbackProfiles);
        }
      } catch (error) {
        console.warn('Failed to fetch voice profiles, using defaults:', error);
        setVoiceProfiles(['SECURE_MALE', 'SECURE_FEMALE', 'ROBOTIC', 'DEEP_SECURE', 'ANONYMOUS']);
      }
    } catch (error) {
      console.error('Error fetching voice settings:', error);
      setVoiceSettingsError(error instanceof Error ? error.message : 'Failed to load voice settings');
      setVoiceSettings(null);
    } finally {
      setVoiceSettingsLoading(false);
    }
  };

  // Update user voice profile
  const updateUserVoiceProfile = async (userId: string, profileName: string) => {
    try {
      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch('/api/voice/update-user-profile', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          defaultMorphingProfile: profileName
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update voice profile');
      }

      // Refresh voice settings
      await fetchVoiceSettings(userId);

      // Show success message
      setVoiceSettingsError(null);
      // You could add a success state here if needed
    } catch (error) {
      console.error('Error updating voice profile:', error);
      setVoiceSettingsError(error instanceof Error ? error.message : 'Failed to update voice profile');
    }
  };

  // Fetch voice settings when user is selected and voice tab is active
  useEffect(() => {
    if (selectedUser && activeTab === 'voice') {
      fetchVoiceRecordings(selectedUser._id);
      fetchVoiceSettings(selectedUser._id);
    }
  }, [selectedUser, activeTab]);

  // Fetch voice settings when modulation tab is active
  useEffect(() => {
    if (selectedUser && activeTab === 'modulation') {
      fetchVoiceSettings(selectedUser._id);
    }
  }, [selectedUser, activeTab]);

  // Voice testing functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Stop recording after 5 seconds
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          setIsRecording(false);
        }
      }, 5000);
    } catch (error) {
      console.error('Error starting recording:', error);
      setVoiceSettingsError('Failed to access microphone');
    }
  };

  const testVoiceModulation = async () => {
    if (!audioBlob || !selectedUser) return;

    try {
      setIsProcessing(true);
      setVoiceSettingsError(null);

      const formData = new FormData();
      formData.append('audio', audioBlob, 'test-recording.wav');
      formData.append('profileName', voiceSettings?.defaultMorphingProfile || 'SECURE_MALE');

      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch('/api/voice/test-modulation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to process voice modulation');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      setProcessedAudioUrl(url);
    } catch (error) {
      console.error('Error testing voice modulation:', error);
      setVoiceSettingsError('Failed to test voice modulation');
    } finally {
      setIsProcessing(false);
    }
  };

  // Voice recording management functions
  const handlePlayRecording = async (recordingId: string) => {
    try {
      setPlayingRecording(recordingId);

      // Create audio element and play
      const audio = new Audio(`/api/admin/voice-recordings/${recordingId}/play`);
      audio.onended = () => setPlayingRecording(null);
      audio.onerror = () => {
        setPlayingRecording(null);
        alert('Failed to play recording');
      };

      await audio.play();
    } catch (error) {
      console.error('Error playing recording:', error);
      setPlayingRecording(null);
      alert('Failed to play recording');
    }
  };

  const handleDownloadRecording = async (recordingId: string, filename: string) => {
    try {
      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/voice-recordings/${recordingId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download recording');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `recording_${recordingId}.wav`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading recording:', error);
      alert('Failed to download recording');
    }
  };

  const handleDeleteRecording = async (recordingId: string) => {
    if (!confirm('Are you sure you want to delete this voice recording? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/voice-recordings/${recordingId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete recording');
      }

      // Refresh recordings list
      if (selectedUser) {
        await fetchVoiceRecordings(selectedUser._id);
      }
    } catch (error) {
      console.error('Error deleting recording:', error);
      alert('Failed to delete recording');
    }
  };

  // CRUD Operations
  const handleCreateUser = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      // Validate form
      const errors: Record<string, string> = {};
      if (!createForm.username.trim()) {
        errors.username = 'Username is required';
      } else if (createForm.username.trim().length < 3) {
        errors.username = 'Username must be at least 3 characters long';
      } else if (!/^[a-zA-Z0-9_-]+$/.test(createForm.username.trim())) {
        errors.username = 'Username can only contain letters, numbers, hyphens, and underscores';
      }
      
      if (createForm.email && createForm.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(createForm.email.trim())) {
          errors.email = 'Please enter a valid email address';
        }
      }
      
      if (!createForm.expression.trim()) {
        errors.expression = 'Mathematical expression is required';
      } else {
        // Basic math expression validation
        try {
          // Check if it contains basic math operations
          if (!/[\+\-\*\/\(\)\d]/.test(createForm.expression.trim())) {
            errors.expression = 'Expression must contain valid mathematical operations (+, -, *, /, parentheses, numbers)';
          }
        } catch (e) {
          errors.expression = 'Invalid mathematical expression';
        }
      }
      
      if (!createForm.displayName.trim()) {
        errors.displayName = 'Display name is required';
      } else if (createForm.displayName.trim().length < 2) {
        errors.displayName = 'Display name must be at least 2 characters long';
      }

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      // Prepare the request payload
      const payload = {
        username: createForm.username.trim(),
        email: createForm.email.trim() || undefined, // Only include if provided
        expression: createForm.expression.trim(),
        displayName: createForm.displayName.trim(),
        isSuperuser: createForm.isSuperuser
      };

      console.log('Creating user with payload:', payload);

      const response = await apiClient.post('/api/admin/users', payload);
      console.log('User created successfully:', response.data);

      // Reset form and close modal
      setCreateForm({
        username: '',
        email: '',
        expression: '',
        displayName: '',
        isSuperuser: false
      });
      setShowCreateModal(false);

      // Refresh users list
      await fetchUsers();

    } catch (error: any) {
      console.error('Create user error:', error);
      console.error('Error response:', error.response?.data);
      setFormErrors({
        submit: error.response?.data?.error || error.message || 'Failed to create user'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditUser = async () => {
    if (!editingUser) return;

    try {
      setSubmitting(true);
      setFormErrors({});

      // Validate form
      const errors: Record<string, string> = {};
      if (!editForm.displayName.trim()) {
        errors.displayName = 'Display name is required';
      } else if (editForm.displayName.trim().length < 2) {
        errors.displayName = 'Display name must be at least 2 characters long';
      }
      
      if (!editForm.expression.trim()) {
        errors.expression = 'Mathematical expression is required';
      } else {
        // Basic math expression validation
        try {
          if (!/[\+\-\*\/\(\)\d]/.test(editForm.expression.trim())) {
            errors.expression = 'Expression must contain valid mathematical operations (+, -, *, /, parentheses, numbers)';
          }
        } catch (e) {
          errors.expression = 'Invalid mathematical expression';
        }
      }

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      // Prepare the request payload
      const payload = {
        displayName: editForm.displayName.trim(),
        expression: editForm.expression.trim(),
        isSuperuser: editForm.isSuperuser,
        status: editForm.status
      };

      console.log('Updating user with payload:', payload);

      const response = await apiClient.put(`/api/admin/users/${editingUser._id}`, payload);
      console.log('User updated successfully:', response.data);

      // Close modal and refresh
      setShowEditModal(false);
      setEditingUser(null);
      await fetchUsers();

    } catch (error: any) {
      console.error('Update user error:', error);
      console.error('Error response:', error.response?.data);
      setFormErrors({
        submit: error.response?.data?.error || error.message || 'Failed to update user'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteUsers = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      // Verify admin password first
      if (!passwordConfirm.password) {
        setFormErrors({ password: 'Admin password is required' });
        return;
      }

      // TODO: Add password verification endpoint call here
      // For now, we'll proceed with deletion

      const usersToDelete = selectedUserIds.length > 0 ? selectedUserIds :
                           selectedUser ? [selectedUser._id] : [];

      if (usersToDelete.length === 0) {
        setFormErrors({ submit: 'No users selected for deletion' });
        return;
      }

      // Delete users one by one
      for (const userId of usersToDelete) {
        await apiClient.delete(`/api/admin/users/${userId}`);
      }

      // Reset states and refresh
      setSelectedUserIds([]);
      setSelectAll(false);
      setShowDeleteModal(false);
      setPasswordConfirm({ password: '' });
      await fetchUsers();

    } catch (error: any) {
      setFormErrors({
        submit: error.response?.data?.error || 'Failed to delete users'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Device and Session Management Functions
  const handleResetUserDevices = async (userId: string, username: string) => {
    if (!confirm(`Are you sure you want to reset all devices for user "${username}"? This will force them to re-register all devices.`)) {
      return;
    }

    try {
      setSubmitting(true);
      await apiClient.post(`/api/admin/users/${userId}/reset-devices`);
      
      // Refresh user data
      await fetchUsers();
      if (selectedUser?._id === userId) {
        const updatedUser = users.find(u => u._id === userId);
        if (updatedUser) setSelectedUser(updatedUser);
      }
      
      alert('User devices have been reset successfully');
    } catch (error) {
      console.error('Error resetting user devices:', error);
      alert('Failed to reset user devices');
    } finally {
      setSubmitting(false);
    }
  };

  const handleResetUserBLE = async (userId: string, username: string) => {
    if (!confirm(`Are you sure you want to reset all BLE devices for user "${username}"? This will unpair all Bluetooth devices.`)) {
      return;
    }

    try {
      setSubmitting(true);
      await apiClient.post(`/api/admin/users/${userId}/reset-ble`);
      
      // Refresh user data
      await fetchUsers();
      if (selectedUser?._id === userId) {
        const updatedUser = users.find(u => u._id === userId);
        if (updatedUser) setSelectedUser(updatedUser);
      }
      
      alert('User BLE devices have been reset successfully');
    } catch (error) {
      console.error('Error resetting user BLE devices:', error);
      alert('Failed to reset user BLE devices');
    } finally {
      setSubmitting(false);
    }
  };

  const handleResetUserSessions = async (userId: string, username: string) => {
    if (!confirm(`Are you sure you want to reset all active sessions for user "${username}"? This will log them out from all devices.`)) {
      return;
    }

    try {
      setSubmitting(true);
      await apiClient.post(`/api/admin/users/${userId}/reset-sessions`);
      
      // Refresh user data
      await fetchUsers();
      if (selectedUser?._id === userId) {
        const updatedUser = users.find(u => u._id === userId);
        if (updatedUser) setSelectedUser(updatedUser);
      }
      
      alert('User sessions have been reset successfully');
    } catch (error) {
      console.error('Error resetting user sessions:', error);
      alert('Failed to reset user sessions');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUnlockUser = async (userId: string, username: string) => {
    if (!confirm(`Are you sure you want to unlock user "${username}" and reset their failed login attempts?`)) {
      return;
    }

    try {
      setSubmitting(true);
      await apiClient.post(`/api/admin/users/${userId}/unlock`);
      
      // Refresh user data
      await fetchUsers();
      if (selectedUser?._id === userId) {
        const updatedUser = users.find(u => u._id === userId);
        if (updatedUser) setSelectedUser(updatedUser);
      }
      
      alert('User has been unlocked successfully');
    } catch (error) {
      console.error('Error unlocking user:', error);
      alert('Failed to unlock user');
    } finally {
      setSubmitting(false);
    }
  };

  const handleResetUserExpression = async (userId: string, username: string, newExpression: string) => {
    if (!newExpression.trim()) {
      alert('Please enter a valid expression');
      return;
    }

    if (!confirm(`Are you sure you want to reset the mathematical expression for user "${username}" to:\n\n"${newExpression.trim()}"`)) {
      return;
    }

    try {
      setSubmitting(true);
      setFormErrors({});
      setSuccessMessage('');
      
      console.log('Sending reset expression request:', {
        userId,
        username,
        expression: newExpression.trim()
      });
      
      await apiClient.post(`/api/admin/users/${userId}/reset-expression`, {
        expression: newExpression.trim()
      });
      
      // Refresh user data
      await fetchUsers();
      if (selectedUser?._id === userId) {
        const updatedUser = users.find(u => u._id === userId);
        if (updatedUser) setSelectedUser(updatedUser);
      }
      
      setSuccessMessage(`Expression updated successfully for user "${username}"`);
      setTimeout(() => setSuccessMessage(''), 3000);
      
    } catch (error: any) {
      console.error('Error resetting user expression:', error);
      
      const errorMessage = error.response?.data?.error || error.message || 'Failed to reset expression';
      setFormErrors({
        submit: `Failed to reset expression for "${username}": ${errorMessage}`
      });
      
      alert(`Error: ${errorMessage}`);
    } finally {
      setSubmitting(false);
    }
  };

  // Selection Management
  const handleSelectUser = (userId: string) => {
    setSelectedUserIds(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedUserIds([]);
    } else {
      setSelectedUserIds(users.map(user => user._id));
    }
    setSelectAll(!selectAll);
  };

  // Modal Management
  const openCreateModal = () => {
    setCreateForm({
      username: '',
      email: '',
      expression: '',
      displayName: '',
      isSuperuser: false
    });
    setFormErrors({});
    setShowCreateModal(true);
  };

  const openEditModal = (user: UserDetails) => {
    setEditingUser(user);
    setEditForm({
      displayName: user.profile?.displayName || '',
      expression: user.mathExpression?.expression || '',
      isSuperuser: user.isSuperuser || false,
      status: user.status
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  const openDeleteModal = () => {
    setPasswordConfirm({ password: '' });
    setFormErrors({});
    setShowDeleteModal(true);
  };

  const closeModals = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setEditingUser(null);
    setFormErrors({});
    setPasswordConfirm({ password: '' });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const renderUserOverview = (user: UserDetails) => (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-50 p-6 rounded-xl">
          <div className="text-2xl font-light text-gray-900 mb-1">{user.devices?.length || 0}</div>
          <div className="text-sm text-gray-500">Devices</div>
        </div>
        <div className="bg-gray-50 p-6 rounded-xl">
          <div className="text-2xl font-light text-gray-900 mb-1">
            {user.devices?.reduce((acc, device) => acc + (device.bleDevices?.length || 0), 0) || 0}
          </div>
          <div className="text-sm text-gray-500">BLE Devices</div>
        </div>
        <div className="bg-gray-50 p-6 rounded-xl">
          <div className="text-2xl font-light text-gray-900 mb-1">{user.chatHistory?.length || 0}</div>
          <div className="text-sm text-gray-500">Chat Sessions</div>
        </div>
        <div className="bg-gray-50 p-6 rounded-xl">
          <div className="text-2xl font-light text-gray-900 mb-1">{user.voiceRecordings?.length || 0}</div>
          <div className="text-sm text-gray-500">Voice Recordings</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* User Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900">User Information</h3>
          
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Username</div>
              <div className="text-gray-900">{user.username}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Display Name</div>
              <div className="text-gray-900">{user.profile?.displayName || 'Not set'}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Email</div>
              <div className="text-gray-900">{user.email || 'Not set'}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Status</div>
              <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                user.status === 'active' ? 'bg-emerald-100 text-emerald-800' :
                user.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                user.status === 'locked' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {user.status?.replace('_', ' ').toUpperCase() || 'Unknown'}
              </span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Math Expression</div>
              <div className="bg-gray-50 px-3 py-2 rounded-lg font-mono text-sm text-gray-900">
                {user.mathExpression?.expression || 'Not set'}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Last Login</div>
              <div className="text-gray-900">
                {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
              </div>
            </div>
          </div>
        </div>

        {/* Admin Actions */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900">Admin Actions</h3>
          
          <div className="space-y-3">
            <button
              onClick={() => handleAdminAction(user._id, 'reset-devices')}
              disabled={actionLoading[user._id]}
              className="w-full px-4 py-3 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-xl border border-orange-200 transition-colors flex items-center space-x-3 disabled:opacity-50"
            >
              {actionLoading[user._id] ? (
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-orange-600 border-t-transparent"></div>
              ) : (
                <XMarkIcon className="w-5 h-5" />
              )}
              <span className="font-medium">Reset All Devices</span>
            </button>
            
            <button
              onClick={() => handleAdminAction(user._id, 'reset-ble')}
              disabled={actionLoading[user._id]}
              className="w-full px-4 py-3 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-xl border border-purple-200 transition-colors flex items-center space-x-3 disabled:opacity-50"
            >
              {actionLoading[user._id] ? (
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-purple-600 border-t-transparent"></div>
              ) : (
                <XMarkIcon className="w-5 h-5" />
              )}
              <span className="font-medium">Reset BLE Devices</span>
            </button>
            
            <button
              onClick={() => handleAdminAction(user._id, 'reset-sessions')}
              disabled={actionLoading[user._id]}
              className="w-full px-4 py-3 bg-red-50 hover:bg-red-100 text-red-700 rounded-xl border border-red-200 transition-colors flex items-center space-x-3 disabled:opacity-50"
            >
              {actionLoading[user._id] ? (
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-red-600 border-t-transparent"></div>
              ) : (
                <XMarkIcon className="w-5 h-5" />
              )}
              <span className="font-medium">Reset All Sessions</span>
            </button>
            
            <button
              onClick={() => handleAdminAction(user._id, 'unlock')}
              disabled={actionLoading[user._id]}
              className="w-full px-4 py-3 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-xl border border-emerald-200 transition-colors flex items-center space-x-3 disabled:opacity-50"
            >
              {actionLoading[user._id] ? (
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-emerald-600 border-t-transparent"></div>
              ) : (
                <LockOpenIcon className="w-5 h-5" />
              )}
              <span className="font-medium">Unlock User Account</span>
            </button>
            
            <button
              onClick={() => handleAdminAction(user._id, 'reset-expression')}
              disabled={actionLoading[user._id]}
              className="w-full px-4 py-3 bg-indigo-50 hover:bg-indigo-100 text-indigo-700 rounded-xl border border-indigo-200 transition-colors flex items-center space-x-3 disabled:opacity-50"
            >
              {actionLoading[user._id] ? (
                <div className="w-5 h-5 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
              ) : (
                <ArrowPathIcon className="w-5 h-5" />
              )}
              <span className="font-medium">Reset Math Expression</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDevicesTab = (user: UserDetails) => (
    <div className="space-y-6">
      {/* Device Details Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Device Fingerprint & Metadata</h3>
          <button
            onClick={() => fetchDeviceDetails(user._id)}
            disabled={deviceLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {deviceLoading ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Refresh</span>
              </>
            )}
          </button>
        </div>

        {deviceError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading device details</h3>
                <div className="mt-2 text-sm text-red-700">{deviceError}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Device Details */}
      {deviceLoading ? (
        <div className="flex justify-center py-8">
          <svg className="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : deviceDetails.length > 0 ? (
        deviceDetails.map((device, index) => (
          <div key={device.deviceId || index} className="bg-white rounded-xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {device.metadata?.model || `Device ${index + 1}`}
                    </h3>
                    <p className="text-sm text-gray-500">ID: {device.deviceId}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    device.isActive ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-700'
                  }`}>
                    {device.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    device.security?.status === 'trusted' ? 'bg-green-100 text-green-700' :
                    device.security?.status === 'suspicious' ? 'bg-yellow-100 text-yellow-700' :
                    device.security?.status === 'blocked' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {device.security?.status || 'Unknown'}
                  </span>
                  <button
                    onClick={() => setExpandedDevice(expandedDevice === device.deviceId ? null : device.deviceId)}
                    className="p-2 text-gray-400 hover:text-gray-600"
                  >
                    <svg className={`h-5 w-5 transform transition-transform ${expandedDevice === device.deviceId ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Basic Device Info */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">Device Type</div>
                  <div className="text-gray-900">{device.metadata?.os || 'Unknown'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">OS Version</div>
                  <div className="text-gray-900">{device.metadata?.osVersion || 'Unknown'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">App Version</div>
                  <div className="text-gray-900">{device.metadata?.appVersion || 'Unknown'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">Last Active</div>
                  <div className="text-gray-900">{device.metadata?.lastActiveAt ? new Date(device.metadata.lastActiveAt).toLocaleDateString() : 'Unknown'}</div>
                </div>
              </div>

              {/* Expanded Details */}
              {expandedDevice === device.deviceId && (
                <div className="border-t border-gray-200 pt-6 space-y-6">
                  {/* Device Fingerprint Components */}
                  {device.fingerprint?.components && (
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-4">Device Fingerprint Components</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">User Agent:</span>
                            <div className="text-gray-900 break-all">{device.fingerprint.components.userAgent || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Screen Resolution:</span>
                            <div className="text-gray-900">{device.fingerprint.components.screenResolution || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Timezone:</span>
                            <div className="text-gray-900">{device.fingerprint.components.timezone || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Language:</span>
                            <div className="text-gray-900">{device.fingerprint.components.language || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Platform:</span>
                            <div className="text-gray-900">{device.fingerprint.components.platform || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Hardware Concurrency:</span>
                            <div className="text-gray-900">{device.fingerprint.components.hardwareConcurrency || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Device Memory:</span>
                            <div className="text-gray-900">{device.fingerprint.components.deviceMemory ? `${device.fingerprint.components.deviceMemory} GB` : 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Color Depth:</span>
                            <div className="text-gray-900">{device.fingerprint.components.colorDepth ? `${device.fingerprint.components.colorDepth} bits` : 'N/A'}</div>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-600">Fingerprint Hash:</span>
                            <code className="text-xs bg-gray-200 px-2 py-1 rounded font-mono">{device.fingerprint.hash}</code>
                          </div>
                          <div className="flex items-center justify-between mt-2">
                            <span className="font-medium text-gray-600">Confidence Score:</span>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              device.fingerprint.confidence >= 80 ? 'bg-green-100 text-green-700' :
                              device.fingerprint.confidence >= 60 ? 'bg-yellow-100 text-yellow-700' :
                              'bg-red-100 text-red-700'
                            }`}>
                              {device.fingerprint.confidence}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Registration Information */}
                  {device.metadata && (
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-4">Registration Information</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">Registered At:</span>
                            <div className="text-gray-900">{device.metadata.registeredAt ? new Date(device.metadata.registeredAt).toLocaleString() : 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Build Version:</span>
                            <div className="text-gray-900">{device.metadata.buildVersion || 'N/A'}</div>
                          </div>
                          {device.metadata.registrationCoords && (
                            <div className="lg:col-span-2">
                              <span className="font-medium text-gray-600">Registration Location:</span>
                              <div className="text-gray-900">
                                Lat: {device.metadata.registrationCoords.lat}, Lng: {device.metadata.registrationCoords.lng}
                                {device.metadata.registrationCoords.accuracy && ` (±${device.metadata.registrationCoords.accuracy}m)`}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3a1 1 0 000 2h.01a1 1 0 100-2H5zm4 0a1 1 0 000 2h.01a1 1 0 100-2H9zm2 0a1 1 0 000 2h.01a1 1 0 100-2H11zm0 4a1 1 0 100 2h.01a1 1 0 100-2H11zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H9zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H7zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H5z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No device details found</h3>
          <p className="text-gray-500">This user hasn't registered any devices with detailed fingerprint data yet</p>
        </div>
      )}

      {/* Legacy Device Summary */}
      {user.devices && user.devices.length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Legacy Device Summary</h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-500">Total Devices</div>
              <div className="text-gray-900">{user.devices.length}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Active Devices</div>
              <div className="text-gray-900">{user.devices.filter(d => d.isActive).length}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">BLE Devices</div>
              <div className="text-gray-900">{user.devices.reduce((sum, d) => sum + (d.bleDevices?.length || 0), 0)}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Verified BLE</div>
              <div className="text-gray-900">{user.devices.reduce((sum, d) => sum + (d.bleDevices?.filter(b => b.isVerified).length || 0), 0)}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderChatTab = (user: UserDetails) => (
    <div className="space-y-6">
      {/* Chat Messages Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Real-time Chat History</h3>
          <button
            onClick={() => fetchChatMessages(user._id)}
            disabled={chatLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {chatLoading ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Refresh</span>
              </>
            )}
          </button>
        </div>

        {chatError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading chat messages</h3>
                <div className="mt-2 text-sm text-red-700">{chatError}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Chat Messages Display */}
      <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div className="max-h-96 overflow-y-auto p-4 space-y-3">
          {chatLoading ? (
            <div className="flex justify-center py-8">
              <svg className="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : chatMessages.length > 0 ? (
            chatMessages.map((message) => (
              <div
                key={message._id}
                className={`flex ${message.senderId.isSuperuser ? 'justify-start' : 'justify-end'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.senderId.isSuperuser
                      ? 'bg-gray-100 text-gray-900'
                      : 'bg-blue-600 text-white'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-xs font-medium">
                      {message.senderId.profile?.displayName || message.senderId.username}
                    </span>
                    {message.isEncrypted && (
                      <svg className="h-3 w-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="text-sm">{message.content.text}</div>
                  {message.mediaAttachment && (
                    <div className="mt-2 p-2 bg-black bg-opacity-10 rounded text-xs">
                      📎 {message.mediaAttachment.filename} ({formatFileSize(message.mediaAttachment.size)})
                    </div>
                  )}
                  <div className="text-xs opacity-75 mt-1">
                    {new Date(message.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
              <p className="text-gray-500">This user hasn't sent any messages yet</p>
            </div>
          )}
        </div>
      </div>

      {/* Session Summary (Legacy) */}
      {user.chatHistory && user.chatHistory.length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Session Summary</h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-500">Total Sessions</div>
              <div className="text-gray-900">{user.chatHistory.length}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Total Messages</div>
              <div className="text-gray-900">{user.chatHistory.reduce((sum, session) => sum + session.messageCount, 0)}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Voice Call Time</div>
              <div className="text-gray-900">{formatDuration(user.chatHistory.reduce((sum, session) => sum + session.voiceCallDuration, 0))}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Encrypted Sessions</div>
              <div className="text-gray-900">{user.chatHistory.filter(session => session.encryptionUsed).length}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderVoiceTab = (user: UserDetails) => (
    <div className="space-y-6">
      {/* Voice Recordings Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Voice Call Recordings</h3>
          <button
            onClick={() => fetchVoiceRecordings(user._id)}
            disabled={voiceLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {voiceLoading ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Refresh</span>
              </>
            )}
          </button>
        </div>

        {voiceError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading voice recordings</h3>
                <div className="mt-2 text-sm text-red-700">{voiceError}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Voice Recordings List */}
      {voiceLoading ? (
        <div className="flex justify-center py-8">
          <svg className="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : voiceRecordings.length > 0 ? (
        voiceRecordings.map((recording, index) => (
          <div key={recording.recordingId || index} className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Recording {recording.recordingId}</h3>
              <div className="flex items-center space-x-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  recording.isProcessed ? 'bg-emerald-100 text-emerald-700' : 'bg-yellow-100 text-yellow-700'
                }`}>
                  {recording.isProcessed ? 'Processed' : 'Processing'}
                </span>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePlayRecording(recording.recordingId)}
                    disabled={playingRecording === recording.recordingId}
                    className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center space-x-1"
                  >
                    {playingRecording === recording.recordingId ? (
                      <>
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span>Playing</span>
                      </>
                    ) : (
                      <>
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                        <span>Play</span>
                      </>
                    )}
                  </button>

                  <button
                    onClick={() => handleDownloadRecording(recording.recordingId, `call_${recording.sessionId}_${recording.recordingId}.wav`)}
                    className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-1"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>Download</span>
                  </button>

                  <button
                    onClick={() => handleDeleteRecording(recording.recordingId)}
                    className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 flex items-center space-x-1"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-5 gap-6">
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Session</div>
                <div className="text-gray-900">{recording.sessionId}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Duration</div>
                <div className="text-gray-900">{formatDuration(recording.duration)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">File Size</div>
                <div className="text-gray-900">{formatFileSize(recording.fileSize)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Voice Profile</div>
                <div className="text-gray-900">{recording.voiceProfile}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Timestamp</div>
                <div className="text-gray-900">{new Date(recording.timestamp).toLocaleDateString()}</div>
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No voice recordings found</h3>
          <p className="text-gray-500">This user hasn't made any voice recordings yet</p>
        </div>
      )}

      {/* Voice Morphing Settings */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">Voice Morphing Settings</h3>
          <button
            onClick={() => fetchVoiceSettings(user._id)}
            disabled={voiceSettingsLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {voiceSettingsLoading ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Refresh</span>
              </>
            )}
          </button>
        </div>

        {voiceSettingsError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading voice settings</h3>
                <div className="mt-2 text-sm text-red-700">{voiceSettingsError}</div>
              </div>
            </div>
          </div>
        )}

        {voiceSettingsLoading ? (
          <div className="flex justify-center py-8">
            <svg className="animate-spin h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        ) : voiceSettings ? (
          <div className="space-y-6">
            {/* Current Voice Profile */}
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="text-md font-medium text-purple-900">Current Voice Profile</h4>
                  <p className="text-sm text-purple-700">Active voice morphing configuration for this user</p>
                </div>
                <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                  {voiceSettings.defaultMorphingProfile?.replace(/_/g, ' ') || 'SECURE_MALE'}
                </span>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-purple-700">Voice Calls:</span>
                  <div className="text-purple-900">{voiceSettings.voiceCallsEnabled ? 'Enabled' : 'Disabled'}</div>
                </div>
                <div>
                  <span className="font-medium text-purple-700">Recording:</span>
                  <div className="text-purple-900">{voiceSettings.recordingEnabled ? 'Enabled' : 'Disabled'}</div>
                </div>
                <div>
                  <span className="font-medium text-purple-700">Custom Profiles:</span>
                  <div className="text-purple-900">{voiceSettings.customProfiles?.length || 0}</div>
                </div>
              </div>
            </div>

            {/* Voice Profile Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Change Voice Morphing Profile
              </label>
              <div className="flex items-center space-x-4">
                <select
                  value={voiceSettings.defaultMorphingProfile || 'SECURE_MALE'}
                  onChange={(e) => updateUserVoiceProfile(user._id, e.target.value)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {voiceProfiles.map((profile) => (
                    <option key={profile} value={profile}>
                      {profile.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
                <div className="text-sm text-gray-500">
                  {voiceProfiles.length} profiles available
                  {user.isSuperuser && (
                    <div className="text-xs text-orange-600 mt-1">
                      ⚠️ NORMAL profile restricted for superusers
                    </div>
                  )}
                </div>
              </div>

              {/* Profile Sample Player */}
              {enhancedProfiles && voiceSettings.defaultMorphingProfile && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Profile Sample</h5>
                  <audio controls className="w-full h-8">
                    <source
                      src={`/api/voice/profile-sample/${voiceSettings.defaultMorphingProfile}`}
                      type="audio/wav"
                    />
                    Your browser does not support audio.
                  </audio>
                </div>
              )}
            </div>

            {/* Voice Profile Details */}
            {voiceSettings.defaultMorphingProfile && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-md font-medium text-gray-900 mb-3">Profile Technical Details</h4>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Security Level:</span>
                    <div className="text-gray-900">Non-reversible</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Encryption:</span>
                    <div className="text-gray-900">Real-time</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Quality:</span>
                    <div className="text-gray-900">High fidelity</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Latency:</span>
                    <div className="text-gray-900">&lt; 50ms</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <h3 className="text-md font-medium text-gray-900 mb-2">No voice settings found</h3>
            <p className="text-gray-500">Voice morphing settings are not configured for this user</p>
          </div>
        )}
      </div>

      {/* Legacy Voice Recordings from User Model */}
      {user.voiceRecordings && user.voiceRecordings.length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Legacy Recordings Summary</h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-500">Total Recordings</div>
              <div className="text-gray-900">{user.voiceRecordings.length}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Total Duration</div>
              <div className="text-gray-900">{formatDuration(user.voiceRecordings.reduce((sum, rec) => sum + rec.duration, 0))}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Total Size</div>
              <div className="text-gray-900">{formatFileSize(user.voiceRecordings.reduce((sum, rec) => sum + rec.fileSize, 0))}</div>
            </div>
            <div>
              <div className="font-medium text-gray-500">Processed</div>
              <div className="text-gray-900">{user.voiceRecordings.filter(rec => rec.isProcessed).length}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderModulationTab = (user: UserDetails) => (
    <div className="space-y-6">
      {/* WORLD Voice Modulation Interface */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">WORLD Voice Modulation for {user.profile?.displayName || user.username}</h3>
            <p className="text-sm text-gray-500">Configure real-time voice transformation with non-reversible morphing</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-500">User Type</div>
              <div className="text-gray-900 font-medium">
                {user.isSuperuser ? 'Superuser' : 'Regular User'}
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setWorldVoiceModulationOpen(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <span>Quick Profile Selection</span>
              </button>
              <button
                onClick={() => setEnhancedVoiceModulationOpen(true)}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
              >
                <span>Custom Voice Profile</span>
              </button>
            </div>
          </div>
        </div>

        {/* Current Profile Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Current Voice Profile</h4>
            <div className="text-lg font-semibold text-gray-900 mb-1">
              {currentVoiceProfile.replace(/_/g, ' ')}
            </div>
            <p className="text-sm text-gray-600">
              {currentVoiceProfile === 'NORMAL_VOICE'
                ? 'Natural voice (regular users only)'
                : 'Secure voice transformation with anti-forensic features'
              }
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Security Features</h4>
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Non-reversible transformation</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Real-time processing (&lt;100ms)</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Anti-forensic protection</span>
              </div>
            </div>
          </div>
        </div>

        {/* Voice Profile Statistics */}
        {voiceSettings && (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">
                {voiceSettings.totalCalls || 0}
              </div>
              <div className="text-sm text-blue-700">Total Voice Calls</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">
                {voiceSettings.averageLatency || 0}ms
              </div>
              <div className="text-sm text-green-700">Average Latency</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">
                {voiceSettings.voiceCallsEnabled ? 'Active' : 'Disabled'}
              </div>
              <div className="text-sm text-purple-700">Voice Calls Status</div>
            </div>
          </div>
        )}
      </div>

      {/* Legacy Voice Neutralization (for backward compatibility) */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Legacy Voice Neutralization</h3>
            <p className="text-sm text-gray-500">Previous voice processing system (deprecated)</p>
          </div>
          <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
            Deprecated
          </span>
        </div>

        <VoiceNeutralization />
      </div>

      {/* WORLD Voice Modulation Panel */}
      <WorldVoiceModulationPanel
        userId={user._id}
        currentProfile={currentVoiceProfile}
        onProfileChange={setCurrentVoiceProfile}
        isOpen={worldVoiceModulationOpen}
        onClose={() => setWorldVoiceModulationOpen(false)}
      />
    </div>
  );

  const renderSecurityTab = (user: UserDetails) => (
    <div className="space-y-6">
      {user.securityEvents?.map((event, index) => (
        <div key={index} className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">{event.eventType.replace('_', ' ').toUpperCase()}</h3>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                event.severity === 'critical' ? 'bg-red-100 text-red-700' :
                event.severity === 'high' ? 'bg-orange-100 text-orange-700' :
                event.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                'bg-emerald-100 text-emerald-700'
              }`}>
                {event.severity.toUpperCase()}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                event.resolved ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
              }`}>
                {event.resolved ? 'Resolved' : 'Open'}
              </span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-500 mb-3">Event Details</div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <pre className="text-sm text-gray-700 overflow-x-auto whitespace-pre-wrap">
                {JSON.stringify(event.details, null, 2)}
              </pre>
            </div>
          </div>
          
          <div className="text-sm text-gray-500">
            {new Date(event.timestamp).toLocaleDateString()}
          </div>
        </div>
      ))}
      {(!user.securityEvents || user.securityEvents.length === 0) && (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No security events</h3>
          <p className="text-gray-500">No security events have been recorded for this user</p>
        </div>
      )}
    </div>
  );

  const renderLogsTab = (user: UserDetails) => (
    <div className="space-y-6">
      {user.loginHistory?.map((login, index) => (
        <div key={index} className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Login Attempt</h3>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              login.success ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
            }`}>
              {login.success ? 'Success' : 'Failed'}
            </span>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">IP Address</div>
              <div className="text-gray-900">{login.ipAddress}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Location</div>
              <div className="text-gray-900">{login.location ? `${login.location.city}, ${login.location.country}` : 'Unknown'}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 mb-1">Timestamp</div>
              <div className="text-gray-900">{new Date(login.timestamp).toLocaleDateString()}</div>
            </div>
          </div>

          {!login.success && login.failureReason && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="text-sm font-medium text-red-800 mb-1">Failure Reason</div>
              <div className="text-sm text-red-700">{login.failureReason}</div>
            </div>
          )}
          
          <div>
            <div className="text-sm font-medium text-gray-500 mb-2">User Agent</div>
            <div className="bg-white p-3 rounded-lg border border-gray-200 text-sm text-gray-700 break-all">
              {login.userAgent}
            </div>
          </div>
        </div>
      ))}
      {(!user.loginHistory || user.loginHistory.length === 0) && (
        <div className="text-center py-16">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No login history</h3>
          <p className="text-gray-500">This user has no recorded login attempts</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-25 p-8">
      {/* Global Success Message */}
      {successMessage && (
        <div className="fixed top-8 right-8 z-50 max-w-sm w-full animate-in slide-in-from-top-4 duration-300">
          <div className="bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-xl shadow-sm flex items-center space-x-3">
            <CheckIcon className="w-5 h-5 text-emerald-600" />
            <span className="font-medium">{successMessage}</span>
          </div>
        </div>
      )}
      
      {/* Global Error Message */}
      {formErrors.submit && (
        <div className="fixed top-8 right-8 z-50 max-w-sm w-full animate-in slide-in-from-top-4 duration-300">
          <div className="bg-red-50 border border-red-200 text-red-800 px-6 py-4 rounded-xl shadow-sm flex items-center space-x-3">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
            <span className="font-medium">{formErrors.submit}</span>
            <button 
              onClick={() => setFormErrors({})}
              className="ml-2 text-red-600 hover:text-red-700 transition-colors"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-12">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-end space-y-6 lg:space-y-0">
            <div className="space-y-2">
              <h1 className="text-4xl font-light text-gray-900 tracking-tight">User Management</h1>
              <p className="text-lg text-gray-500 font-light">Monitor and manage user accounts, devices, and sessions</p>
              <div className="flex items-center space-x-4 pt-2">
                <div className="text-sm text-gray-400">
                  <span className="font-medium text-gray-900">{users.length}</span> total users
                </div>
                {selectedUserIds.length > 0 && (
                  <div className="text-sm text-blue-600">
                    <span className="font-medium">{selectedUserIds.length}</span> selected
                  </div>
                )}
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <button
                onClick={openCreateModal}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium flex items-center justify-center space-x-3 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <UserPlusIcon className="w-5 h-5" />
                <span>Create User</span>
              </button>
              {selectedUserIds.length > 0 && (
                <button
                  onClick={openDeleteModal}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl font-medium flex items-center justify-center space-x-3 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <TrashIcon className="w-5 h-5" />
                  <span>Delete ({selectedUserIds.length})</span>
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Users List */}
          <div className="lg:col-span-4">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
              <div className="p-8 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-medium text-gray-900">Users</h2>
                  {users.length > 0 && (
                    <label className="flex items-center space-x-3 cursor-pointer group">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                      />
                      <span className="text-sm text-gray-500 group-hover:text-gray-700 transition-colors">Select All</span>
                    </label>
                  )}
                </div>
              </div>
              <div className="max-h-[600px] overflow-y-auto">
                {loading ? (
                  <div className="p-8 text-center">
                    <div className="inline-flex items-center space-x-3">
                      <div className="w-5 h-5 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                      <span className="text-gray-500">Loading users...</span>
                    </div>
                  </div>
                ) : error ? (
                  <div className="p-8 text-center">
                    <div className="text-red-600 font-medium">Error loading users</div>
                    <div className="text-sm text-gray-500 mt-1">{error}</div>
                  </div>
                ) : users.length === 0 ? (
                  <div className="p-8 text-center">
                    <UsersIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                    <p className="text-gray-500">Get started by creating your first user</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-50">
                    {users.map((user) => (
                      <div
                        key={user._id}
                        className={`p-6 hover:bg-gray-25 transition-colors cursor-pointer ${
                          selectedUser?._id === user._id ? 'bg-blue-25 border-r-4 border-blue-500' : ''
                        }`}
                      >
                        <div className="flex items-start space-x-4">
                          <input
                            type="checkbox"
                            checked={selectedUserIds.includes(user._id)}
                            onChange={() => handleSelectUser(user._id)}
                            className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 mt-1 flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div
                            className="flex-1 min-w-0"
                            onClick={() => setSelectedUser(user)}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-medium text-gray-900 truncate text-lg">{user.username}</h3>
                              <div className="flex items-center space-x-2 flex-shrink-0">
                                {user.isSuperuser && (
                                  <span className="px-2.5 py-1 rounded-full text-xs bg-purple-100 text-purple-700 font-medium">
                                    Admin
                                  </span>
                                )}
                                <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${
                                  user.isActive ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
                                }`}>
                                  {user.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm text-gray-600 truncate">{user.email || 'No email set'}</p>
                              <p className="text-sm text-gray-500 truncate">{user.profile?.displayName || 'No display name'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* User Details */}
          <div className="lg:col-span-8">
            {selectedUser ? (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
                <div className="p-8 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-medium text-gray-900">{selectedUser.username}</h2>
                      <p className="text-gray-500 mt-1">{selectedUser.email || 'No email set'}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => openEditModal(selectedUser)}
                        className="px-4 py-2 bg-blue-50 text-blue-700 rounded-xl hover:bg-blue-100 transition-colors flex items-center space-x-2"
                      >
                        <PencilIcon className="w-4 h-4" />
                        <span>Edit</span>
                      </button>
                      {!selectedUser.isSuperuser && (
                        <button
                          onClick={() => {
                            openDeleteModal();
                          }}
                          className="px-4 py-2 bg-red-50 text-red-700 rounded-xl hover:bg-red-100 transition-colors flex items-center space-x-2"
                        >
                          <TrashIcon className="w-4 h-4" />
                          <span>Delete</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-100">
                  {/* Tab icon mapping and rendering */}
                  {(() => {
                    const tabIconMap: Record<string, React.ElementType | undefined> = {
                      overview: UsersIcon,
                      devices: ArrowPathIcon,
                      chat: EyeIcon,
                      voice: UserPlusIcon,
                      modulation: PencilIcon,
                      recordings: TrashIcon,
                      security: LockOpenIcon,
                      logs: ExclamationTriangleIcon
                    };
                    const tabs = [
                      { key: 'overview', label: 'Overview' },
                      { key: 'devices', label: 'Devices' },
                      { key: 'chat', label: 'Chat History' },
                      { key: 'voice', label: 'Voice Recordings' },
                      { key: 'modulation', label: 'Voice Modulation' },
                      { key: 'recordings', label: 'Voice Recordings' },
                      { key: 'security', label: 'Security Events' },
                      { key: 'logs', label: 'Login Logs' }
                    ];
                    return (
                      <nav className="flex space-x-8 px-8">
                        {tabs.map((tab) => {
                          const TabIcon = tabIconMap[tab.key];
                          return (
                            <button
                              key={tab.key}
                              onClick={() => setActiveTab(tab.key as any)}
                              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                                activeTab === tab.key
                                  ? 'border-blue-500 text-blue-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                              }`}
                            >
                              {TabIcon && <TabIcon className="w-4 h-4 mr-1 inline-block align-text-bottom" />}
                              {tab.label}
                            </button>
                          );
                        })}
                      </nav>
                    );
                  })()}
                </div>

                {/* Tab Content */}
                <div className="p-8">
                  {activeTab === 'overview' && renderUserOverview(selectedUser)}
                  {activeTab === 'devices' && renderDevicesTab(selectedUser)}
                  {activeTab === 'chat' && renderChatTab(selectedUser)}
                  {activeTab === 'voice' && renderVoiceTab(selectedUser)}
                  {activeTab === 'modulation' && renderModulationTab(selectedUser)}
                  {activeTab === 'recordings' && <VoiceRecordingsManagement />}
                  {activeTab === 'security' && renderSecurityTab(selectedUser)}
                  {activeTab === 'logs' && renderLogsTab(selectedUser)}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-16 text-center">
                <div className="max-w-sm mx-auto">
                  <UsersIcon className="w-16 h-16 text-gray-300 mx-auto mb-6" />
                  <h3 className="text-xl font-medium text-gray-900 mb-3">Select a user to view details</h3>
                  <p className="text-gray-500">Choose a user from the list to view their information, devices, and activity</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Create User Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Create New User</h3>
                <button
                  onClick={closeModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleCreateUser(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username *
                    </label>
                    <input
                      type="text"
                      value={createForm.username}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, username: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter username"
                    />
                    {formErrors.username && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.username}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      value={createForm.email}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter email (optional)"
                    />
                    {formErrors.email && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Name *
                    </label>
                    <input
                      type="text"
                      value={createForm.displayName}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, displayName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter display name"
                    />
                    {formErrors.displayName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.displayName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Math Expression *
                    </label>
                    <input
                      type="text"
                      value={createForm.expression}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, expression: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 2+3*4"
                    />
                    {formErrors.expression && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.expression}</p>
                    )}
                  </div>

                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={createForm.isSuperuser}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, isSuperuser: e.target.checked }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">Make this user a superuser</span>
                    </label>
                  </div>

                  {formErrors.submit && (
                    <div className="text-red-500 text-sm">{formErrors.submit}</div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {submitting ? 'Creating...' : 'Create User'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Edit User Modal */}
        {showEditModal && editingUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Edit User: {editingUser.username}</h3>
                <button
                  onClick={closeModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleEditUser(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Name *
                    </label>
                    <input
                      type="text"
                      value={editForm.displayName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, displayName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter display name"
                    />
                    {formErrors.displayName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.displayName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Math Expression *
                    </label>
                    <input
                      type="text"
                      value={editForm.expression}
                      onChange={(e) => setEditForm(prev => ({ ...prev, expression: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 2+3*4"
                    />
                    {formErrors.expression && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.expression}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={editForm.status}
                      onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="locked">Locked</option>
                      <option value="pending_device_registration">Pending Device Registration</option>
                    </select>
                  </div>

                  {!editingUser.isSuperuser && (
                    <div>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.isSuperuser}
                          onChange={(e) => setEditForm(prev => ({ ...prev, isSuperuser: e.target.checked }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">Make this user a superuser</span>
                      </label>
                    </div>
                  )}

                  {formErrors.submit && (
                    <div className="text-red-500 text-sm">{formErrors.submit}</div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {submitting ? 'Updating...' : 'Update User'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="w-8 h-8 text-red-500 mr-3" />
                <h3 className="text-lg font-semibold">Confirm Deletion</h3>
              </div>

              <div className="mb-4">
                <p className="text-gray-600 mb-2">
                  {selectedUserIds.length > 0
                    ? `Are you sure you want to delete ${selectedUserIds.length} selected user(s)?`
                    : selectedUser
                      ? `Are you sure you want to delete user "${selectedUser.username}"?`
                      : 'Are you sure you want to delete this user?'
                  }
                </p>
                <p className="text-sm text-red-600">This action cannot be undone.</p>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleDeleteUsers(); }}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Admin Password *
                  </label>
                  <input
                    type="password"
                    value={passwordConfirm.password}
                    onChange={(e) => setPasswordConfirm({ password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                    placeholder="Enter your admin password"
                  />
                  {formErrors.password && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.password}</p>
                  )}
                </div>

                {formErrors.submit && (
                  <div className="text-red-500 text-sm mb-4">{formErrors.submit}</div>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    {submitting ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Admin Action Confirmation Modal */}
        {showActionConfirm && pendingAction && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl p-8 w-full max-w-md shadow-xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mr-4">
                  <ExclamationTriangleIcon className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="text-xl font-medium text-gray-900">Confirm Admin Action</h3>
              </div>

              <div className="mb-8">
                <p className="text-gray-700 mb-3 text-lg">
                  Are you sure you want to {pendingAction.actionLabel.toLowerCase()}?
                </p>
                <div className="bg-orange-50 border border-orange-200 rounded-xl p-4">
                  <p className="text-sm text-orange-800 font-medium">
                    ⚠️ This action cannot be undone and may require the user to reconfigure their settings.
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowActionConfirm(false);
                    setPendingAction(null);
                  }}
                  className="px-6 py-3 text-gray-700 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={executeAdminAction}
                  disabled={pendingAction && actionLoading[pendingAction.userId]}
                  className="px-6 py-3 bg-orange-600 text-white rounded-xl hover:bg-orange-700 disabled:opacity-50 transition-colors font-medium flex items-center space-x-2"
                >
                  {pendingAction && actionLoading[pendingAction.userId] ? (
                    <>
                      <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <span>Confirm Action</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Voice Modulation Panel */}
        <EnhancedVoiceModulationPanel
          isOpen={enhancedVoiceModulationOpen}
          onClose={() => setEnhancedVoiceModulationOpen(false)}
          userId={selectedUser?._id}
          currentProfile={currentVoiceProfile}
          onProfileUpdate={(profileName) => {
            setCurrentVoiceProfile(profileName);
            // TODO: Update user's voice profile in backend
            console.log('Updated voice profile to:', profileName);
          }}
        />
      </div>
    </div>
  );
};

export default withAdminAuth(UsersManagement);
